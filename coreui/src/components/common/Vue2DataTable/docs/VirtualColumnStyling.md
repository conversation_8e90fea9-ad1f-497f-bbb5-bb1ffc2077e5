# Virtual Column Styling Guide

This comprehensive guide covers all aspects of styling virtual columns in the Vue2DataTable component, including CSS class application, HTML content styling, conditional styling, and best practices for maintaining responsive design and theme consistency.

## Overview

Virtual columns support multiple levels of styling customization:

1. **Cell-level styling** - Apply CSS classes to the entire table cell
2. **Content-level styling** - Style the content wrapper within the cell
3. **Formatted content styling** - Style the actual formatted content
4. **HTML content styling** - Use HTML with inline styles or CSS classes
5. **Conditional styling** - Apply different styles based on data values

## 1. CSS Class Application Methods

### Static CSS Classes

Apply fixed CSS classes to virtual columns:

```javascript
{
  key: 'full_name',
  label: 'Full Name',
  virtual: true,
  formatter: (item) => `${item.first_name} ${item.last_name}`,
  cellClass: 'custom-name-cell',           // Cell-level class
  contentClass: 'name-content',            // Content wrapper class
  formattedClass: 'formatted-name'         // Formatted content class
}
```

### Dynamic CSS Classes with Functions

Use functions to apply classes based on data:

```javascript
{
  key: 'salary_display',
  label: 'Salary',
  virtual: true,
  formatter: (item) => formatCurrency(item.salary),
  cellClass: (item) => {
    if (item.salary >= 100000) return 'high-salary'
    if (item.salary >= 75000) return 'medium-salary'
    return 'low-salary'
  },
  contentClass: (item) => item.salary >= 90000 ? 'premium-content' : 'standard-content',
  formattedClass: (item) => ['currency-value', item.salary >= 100000 ? 'large' : 'normal']
}
```

### Multiple CSS Classes

Apply multiple classes using arrays:

```javascript
{
  key: 'status_display',
  label: 'Status',
  virtual: true,
  formatter: (item) => item.status,
  cellClass: ['status-cell', 'bordered'],
  contentClass: (item) => ['status-content', `status-${item.status}`, 'centered'],
  formattedClass: (item) => {
    const classes = ['status-badge']
    if (item.priority === 'high') classes.push('urgent')
    if (item.active) classes.push('active')
    return classes
  }
}
```

## 2. HTML Content Styling

### Basic HTML with CSS Classes

```javascript
{
  key: 'employee_card',
  label: 'Employee',
  virtual: true,
  formatter: (item) => {
    const statusClass = `status-badge status-${item.status}`
    const priorityIcon = item.priority === 'high' ? '🔥' : '📋'
    
    return `
      <div class="employee-card">
        <div class="employee-name">${item.first_name} ${item.last_name}</div>
        <div class="${statusClass}">${priorityIcon} ${item.status.toUpperCase()}</div>
      </div>
    `
  }
}
```

### Rich HTML Content with Inline Styles

```javascript
{
  key: 'progress_display',
  label: 'Progress',
  virtual: true,
  formatter: (item) => {
    const percentage = item.completion_rate
    const color = percentage >= 80 ? '#10b981' : percentage >= 60 ? '#f59e0b' : '#ef4444'
    
    return `
      <div style="display: flex; align-items: center; gap: 8px;">
        <div style="flex: 1; height: 8px; background: #f3f4f6; border-radius: 4px; overflow: hidden;">
          <div style="height: 100%; background: ${color}; width: ${percentage}%; transition: width 0.3s ease;"></div>
        </div>
        <span style="font-size: 12px; font-weight: 600; color: ${color};">${percentage}%</span>
      </div>
    `
  }
}
```

### Profile Cards with Images

```javascript
{
  key: 'user_profile',
  label: 'Profile',
  virtual: true,
  formatter: (item) => {
    const avatar = item.avatar || '/default-avatar.png'
    const statusColor = item.online ? '#10b981' : '#6b7280'
    
    return `
      <div class="profile-card">
        <div class="avatar-container">
          <img src="${avatar}" alt="Avatar" class="avatar" />
          <div class="status-dot" style="background-color: ${statusColor};"></div>
        </div>
        <div class="profile-info">
          <div class="name">${item.first_name} ${item.last_name}</div>
          <div class="role">${item.role}</div>
        </div>
      </div>
    `
  }
}
```

## 3. Conditional Styling Techniques

### Performance-Based Styling

```javascript
{
  key: 'performance_score',
  label: 'Performance',
  virtual: true,
  formatter: (item) => {
    const score = item.performance_score
    const stars = '⭐'.repeat(Math.ceil(score / 20))
    return `${score}% ${stars}`
  },
  cellClass: (item) => {
    const score = item.performance_score
    if (score >= 90) return 'excellent-performance'
    if (score >= 80) return 'good-performance'
    if (score >= 70) return 'average-performance'
    return 'poor-performance'
  },
  formattedClass: (item) => {
    const baseClasses = ['performance-value']
    if (item.performance_score >= 85) baseClasses.push('highlighted')
    return baseClasses
  }
}
```

### Status-Based Color Coding

```javascript
{
  key: 'order_status',
  label: 'Order Status',
  virtual: true,
  formatter: (item) => {
    const statusConfig = {
      pending: { icon: '⏳', color: '#f59e0b', bg: '#fef3c7' },
      processing: { icon: '⚙️', color: '#3b82f6', bg: '#dbeafe' },
      completed: { icon: '✅', color: '#10b981', bg: '#d1fae5' },
      cancelled: { icon: '❌', color: '#ef4444', bg: '#fee2e2' }
    }
    
    const config = statusConfig[item.status] || statusConfig.pending
    
    return `
      <div class="status-badge" style="
        background: ${config.bg}; 
        color: ${config.color}; 
        border: 1px solid ${config.color}40;
        padding: 4px 8px; 
        border-radius: 12px; 
        font-size: 12px; 
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 4px;
      ">
        <span>${config.icon}</span>
        <span>${item.status.toUpperCase()}</span>
      </div>
    `
  }
}
```

### Priority-Based Styling

```javascript
{
  key: 'task_priority',
  label: 'Priority',
  virtual: true,
  formatter: (item) => {
    const priorityMap = {
      low: { color: '#10b981', label: 'Low Priority' },
      medium: { color: '#f59e0b', label: 'Medium Priority' },
      high: { color: '#ef4444', label: 'High Priority' },
      urgent: { color: '#dc2626', label: 'URGENT' }
    }
    
    const config = priorityMap[item.priority]
    
    return `
      <div class="priority-indicator">
        <div class="priority-dot" style="
          width: 8px; 
          height: 8px; 
          border-radius: 50%; 
          background: ${config.color};
          display: inline-block;
          margin-right: 6px;
        "></div>
        <span style="color: ${config.color}; font-weight: 600; font-size: 12px;">
          ${config.label}
        </span>
      </div>
    `
  },
  cellClass: (item) => `priority-${item.priority}`,
  contentClass: (item) => item.priority === 'urgent' ? 'urgent-content' : 'normal-content'
}
```

## 4. Pre-built Style Classes

The Vue2DataTable includes several pre-built CSS classes for common use cases:

### Status Badges
```css
.status-badge.status-active    /* Green active status */
.status-badge.status-inactive  /* Red inactive status */
.status-badge.status-pending   /* Yellow pending status */
.status-badge.status-completed /* Blue completed status */
.status-badge.status-cancelled /* Gray cancelled status */
```

### Currency Values
```css
.currency-value          /* Monospace font for currency */
.currency-value.negative /* Red color for negative values */
.currency-value.large    /* Larger font size for emphasis */
```

### Percentage Values
```css
.percentage-value.positive /* Green for positive percentages */
.percentage-value.negative /* Red for negative percentages */
.percentage-value.neutral  /* Gray for neutral percentages */
```

### Profile Cards
```css
.profile-card       /* Flex container for profile display */
.profile-card .avatar /* Circular avatar styling */
.profile-card .info   /* Profile information container */
```

### Progress Bars
```css
.progress-bar       /* Progress bar container */
.progress-bar .bar  /* Progress bar track */
.progress-bar .fill /* Progress bar fill */
```

## 5. Responsive Design Considerations

### Mobile-Friendly Styling

```javascript
{
  key: 'responsive_content',
  label: 'Content',
  virtual: true,
  formatter: (item) => {
    return `
      <div class="responsive-content">
        <div class="desktop-view">${item.full_description}</div>
        <div class="mobile-view">${item.short_description}</div>
      </div>
    `
  }
}
```

```css
.responsive-content .mobile-view {
  display: none;
}

@media (max-width: 768px) {
  .responsive-content .desktop-view {
    display: none;
  }
  
  .responsive-content .mobile-view {
    display: block;
  }
}
```

### Adaptive Font Sizes

```css
.table-cell--virtual {
  font-size: 14px;
}

@media (max-width: 768px) {
  .table-cell--virtual {
    font-size: 12px;
  }
  
  .table-cell--virtual .status-badge {
    padding: 2px 6px;
    font-size: 10px;
  }
}
```

## 6. Theme Integration

### Dark Theme Support

```css
.vue2-datatable--dark {
  .table-cell--virtual {
    background: #1f2937;
    color: #f9fafb;
  }
  
  .status-badge.status-active {
    background: #065f46;
    color: #d1fae5;
  }
  
  .currency-value {
    color: #34d399;
  }
}
```

### Custom Theme Variables

```css
:root {
  --virtual-column-accent: #3b82f6;
  --virtual-column-bg: #f8fafc;
  --virtual-column-border: #e2e8f0;
}

.table-cell--virtual {
  background: var(--virtual-column-bg);
  border-left: 3px solid var(--virtual-column-accent);
}
```

## 7. Performance Best Practices

### Efficient CSS Classes

```javascript
// Good: Simple class application
cellClass: 'status-cell'

// Good: Conditional with caching
cellClass: (item) => item._cachedStatusClass || (item._cachedStatusClass = getStatusClass(item))

// Avoid: Complex calculations in class functions
cellClass: (item) => {
  // Expensive calculation on every render
  return calculateComplexClass(item)
}
```

### Optimized HTML Content

```javascript
// Good: Simple HTML structure
formatter: (item) => `<span class="status-${item.status}">${item.status}</span>`

// Good: Cached complex HTML
formatter: (item) => {
  if (!item._cachedHTML) {
    item._cachedHTML = generateComplexHTML(item)
  }
  return item._cachedHTML
}
```

## 8. Accessibility Considerations

### ARIA Labels and Roles

```javascript
{
  key: 'accessible_status',
  label: 'Status',
  virtual: true,
  formatter: (item) => {
    return `
      <div class="status-indicator" 
           role="status" 
           aria-label="Current status: ${item.status}">
        <span class="status-icon" aria-hidden="true">●</span>
        <span class="status-text">${item.status}</span>
      </div>
    `
  }
}
```

### Color Contrast

```css
.status-badge {
  /* Ensure sufficient contrast ratios */
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-badge.high-contrast {
  background: #ffffff;
  color: #000000;
  border: 2px solid #374151;
}
```

## 9. Common Styling Patterns

### Gradient Backgrounds

```css
.premium-cell {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}
```

### Animated Elements

```css
.loading-cell {
  position: relative;
  overflow: hidden;
}

.loading-cell::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}
```

### Hover Effects

```css
.interactive-cell {
  transition: all 0.2s ease;
  cursor: pointer;
}

.interactive-cell:hover {
  background: #f0f9ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
```

## 10. Styling Utilities

### Pre-built Formatter Functions

```javascript
// Currency formatter with styling
export const currencyFormatter = (value, options = {}) => {
  const { currency = 'USD', locale = 'en-US', className = 'currency-value' } = options
  const formatted = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency
  }).format(value)

  const colorClass = value < 0 ? 'negative' : ''
  return `<span class="${className} ${colorClass}">${formatted}</span>`
}

// Status badge formatter
export const statusBadgeFormatter = (status, options = {}) => {
  const { showIcon = true, customClasses = {} } = options
  const statusConfig = {
    active: { icon: '🟢', class: 'status-active' },
    inactive: { icon: '🔴', class: 'status-inactive' },
    pending: { icon: '⏳', class: 'status-pending' },
    ...customClasses
  }

  const config = statusConfig[status] || statusConfig.pending
  const icon = showIcon ? config.icon : ''

  return `<span class="status-badge ${config.class}">${icon} ${status.toUpperCase()}</span>`
}

// Progress bar formatter
export const progressBarFormatter = (percentage, options = {}) => {
  const { showPercentage = true, height = '6px', color = '#3b82f6' } = options
  const percentageText = showPercentage ? `<span class="percentage">${percentage}%</span>` : ''

  return `
    <div class="progress-bar">
      <div class="bar" style="height: ${height};">
        <div class="fill" style="width: ${percentage}%; background: ${color};"></div>
      </div>
      ${percentageText}
    </div>
  `
}
```

### CSS Class Generators

```javascript
// Generate conditional classes based on value ranges
export const generateRangeClasses = (value, ranges, baseClass = '') => {
  const classes = [baseClass].filter(Boolean)

  for (const range of ranges) {
    if (value >= range.min && value <= range.max) {
      classes.push(range.class)
      break
    }
  }

  return classes
}

// Usage example:
const salaryRanges = [
  { min: 100000, max: Infinity, class: 'high-salary' },
  { min: 75000, max: 99999, class: 'medium-salary' },
  { min: 0, max: 74999, class: 'low-salary' }
]

// In column definition:
cellClass: (item) => generateRangeClasses(item.salary, salaryRanges, 'salary-cell')
```

This comprehensive styling system allows you to create visually appealing, accessible, and performant virtual columns that integrate seamlessly with the Vue2DataTable's existing design system.
