<template>
  <tr
    :class="rowClasses"
    @click="handleClick"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <td
      v-for="column in columns"
      :key="column.key"
      :class="getCellClass(column)"
      :style="getCellStyle(column)"
    >
      <!-- Selection Cell -->
      <div v-if="column.key === 'select'" class="cell-select">
        <input
          type="checkbox"
          class="row-checkbox"
          :checked="selected"
          @change="handleSelect"
          @click.stop
          title="Select Row"
        />
      </div>

      <!-- Custom Slot Content -->
      <template v-else-if="$scopedSlots[column.key]">
        <slot
          :name="column.key"
          :item="item"
          :value="getCellValue(column)"
          :column="column"
          :index="index"
        />
      </template>

      <!-- Formatted Content -->
      <div v-else class="cell-content" :class="getCellContentClass(column)">
        <span
          v-if="column.format || column.formatter"
          v-html="getFormattedValue(column)"
          :class="getFormattedContentClass(column)"
        ></span>
        <span v-else :class="getFormattedContentClass(column)">{{ getCellValue(column) }}</span>
      </div>
    </td>
  </tr>
</template>

<script>
import { applyColumnFormatter, getNestedValue } from '../../utils/formatters.js'

export default {
  name: 'TableRow',

  props: {
    item: {
      type: Object,
      required: true
    },
    index: {
      type: Number,
      required: true
    },
    columns: {
      type: Array,
      required: true
    },
    selected: {
      type: Boolean,
      default: false
    },
    selectable: {
      type: Boolean,
      default: false
    },
    selectOnRowClick: {
      type: Boolean,
      default: true
    },
    multipleSelection: {
      type: Boolean,
      default: true
    },
    rowClass: {
      type: [String, Array, Object],
      default: null
    },
    clickable: {
      type: Boolean,
      default: true
    },
    hoverable: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      isHovered: false
    }
  },

  computed: {
    /**
     * Get row CSS classes
     */
    rowClasses() {
      const classes = ['table-row']

      // Add custom row classes
      if (this.rowClass) {
        if (Array.isArray(this.rowClass)) {
          classes.push(...this.rowClass)
        } else if (typeof this.rowClass === 'object') {
          Object.entries(this.rowClass).forEach(([className, condition]) => {
            if (condition) classes.push(className)
          })
        } else {
          classes.push(this.rowClass)
        }
      }

      // State classes
      if (this.selected) classes.push('table-row--selected')
      if (this.isHovered && this.hoverable) classes.push('table-row--hovered')
      if (this.clickable) classes.push('table-row--clickable')
      if (this.selectable) classes.push('table-row--selectable')
      if (this.selectOnRowClick && this.selectable) classes.push('table-row--select-on-click')

      return classes
    }
  },

  methods: {
    /**
     * Get cell CSS class with virtual column support
     */
    getCellClass(column) {
      const classes = ['table-cell', `table-cell--${column.key}`]

      // Column-specific classes
      if (column.key === 'select') {
        classes.push('table-cell--select')
      }

      // Virtual column classes
      if (column.virtual) {
        classes.push('table-cell--virtual')

        // Add custom CSS class if specified
        if (column.cellClass) {
          if (typeof column.cellClass === 'function') {
            const customClass = column.cellClass(this.item, column, this.index)
            if (customClass) {
              if (Array.isArray(customClass)) {
                classes.push(...customClass)
              } else {
                classes.push(customClass)
              }
            }
          } else if (typeof column.cellClass === 'string') {
            classes.push(column.cellClass)
          } else if (Array.isArray(column.cellClass)) {
            classes.push(...column.cellClass)
          }
        }
      }

      if (column.align) {
        classes.push(`table-cell--${column.align}`)
      }

      if (column.type) {
        classes.push(`table-cell--${column.type}`)
      }

      if (column.fixed) {
        classes.push(`table-cell--fixed-${column.fixed}`)
      }

      return classes
    },

    /**
     * Get cell inline styles
     */
    getCellStyle(column) {
      const styles = {}

      if (column.width) {
        if (typeof column.width === 'number') {
          styles.width = `${column.width}px`
          styles.minWidth = `${column.width}px`
        } else {
          styles.width = column.width
          styles.minWidth = column.width
        }
      }

      if (column.align) {
        styles.textAlign = column.align
      }

      return styles
    },

    /**
     * Get cell value with virtual column support
     */
    getCellValue(column) {
      if (column.key === 'select') return null

      // Handle virtual columns with formatter functions
      if (column.virtual && column.formatter && typeof column.formatter === 'function') {
        return column.formatter(this.item)
      }

      // Use custom getter if provided
      if (column.getter && typeof column.getter === 'function') {
        return column.getter(this.item, column, this.index)
      }

      // For virtual columns without formatter, return empty string
      if (column.virtual) {
        return ''
      }

      // Get nested value for regular columns
      return getNestedValue(this.item, column.key)
    },

    /**
     * Get formatted cell value with enhanced virtual column support
     */
    getFormattedValue(column) {
      // For virtual columns, the formatter is already applied in getCellValue
      if (column.virtual && column.formatter && typeof column.formatter === 'function') {
        return this.getCellValue(column)
      }

      const value = this.getCellValue(column)

      // Apply column formatter (for format objects)
      if (column.format) {
        return applyColumnFormatter(value, column, this.item)
      }

      // Apply custom formatter function for non-virtual columns
      if (column.formatter && typeof column.formatter === 'function' && !column.virtual) {
        return column.formatter(value, this.item, column, this.index)
      }

      return value
    },

    /**
     * Handle row click
     */
    handleClick(event) {
      if (!this.clickable) return

      this.$emit('click', {
        item: this.item,
        index: this.index,
        event,
        selected: this.selected
      })
    },

    /**
     * Handle row selection
     */
    handleSelect(event) {
      this.$emit('select', {
        item: this.item,
        index: this.index,
        selected: event.target.checked,
        event
      })
    },

    /**
     * Handle mouse enter
     */
    handleMouseEnter(event) {
      if (!this.hoverable) return

      this.isHovered = true
      this.$emit('hover', {
        item: this.item,
        index: this.index,
        event,
        type: 'enter'
      })
    },

    /**
     * Handle mouse leave
     */
    handleMouseLeave(event) {
      if (!this.hoverable) return

      this.isHovered = false
      this.$emit('hover', {
        item: this.item,
        index: this.index,
        event,
        type: 'leave'
      })
    },

    /**
     * Get cell content CSS classes
     */
    getCellContentClass(column) {
      const classes = []

      if (column.virtual) {
        classes.push('cell-content--virtual')
      }

      if (column.contentClass) {
        if (typeof column.contentClass === 'function') {
          const customClass = column.contentClass(this.item, column, this.index)
          if (customClass) {
            if (Array.isArray(customClass)) {
              classes.push(...customClass)
            } else {
              classes.push(customClass)
            }
          }
        } else if (typeof column.contentClass === 'string') {
          classes.push(column.contentClass)
        } else if (Array.isArray(column.contentClass)) {
          classes.push(...column.contentClass)
        }
      }

      return classes
    },

    /**
     * Get formatted content CSS classes
     */
    getFormattedContentClass(column) {
      const classes = []

      if (column.virtual) {
        classes.push('formatted-content--virtual')
      }

      if (column.formattedClass) {
        if (typeof column.formattedClass === 'function') {
          const customClass = column.formattedClass(this.item, column, this.index)
          if (customClass) {
            if (Array.isArray(customClass)) {
              classes.push(...customClass)
            } else {
              classes.push(customClass)
            }
          }
        } else if (typeof column.formattedClass === 'string') {
          classes.push(column.formattedClass)
        } else if (Array.isArray(column.formattedClass)) {
          classes.push(...column.formattedClass)
        }
      }

      return classes
    }
  }
}
</script>

<style lang="scss" scoped>
/* Table Row Styles - Matching GenericDataTable Design */

.table-row {
  transition: background-color 0.2s ease, transform 0.1s ease;
  will-change: background-color;
  contain: layout style;
  border-bottom: 1px solid #e2e8f0;

  &--clickable {
    cursor: pointer;
  }

  &--selectable {
    .table-cell--select {
      opacity: 1;
    }
  }

  &--selected {
    background: linear-gradient(135deg, #e6fffa 0%, #d1fae5 100%) !important;
    border-left: 4px solid #10b981;
    box-shadow: 0 2px 8px rgba(16, 185, 129, 0.15);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, rgba(16, 185, 129, 0.05) 0%, transparent 100%);
      pointer-events: none;
    }

    .table-cell {
      background-color: inherit;
      color: #065f46;
      font-weight: 500;
    }

    .row-checkbox {
      accent-color: #10b981;
      transform: scale(1.1);
    }
  }

  &--hovered {
    background-color: #f0f9ff;
    transform: translateZ(0);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);

    .table-cell {
      background-color: inherit;
    }

    &.table-row--selected {
      background: linear-gradient(135deg, #dcfdf7 0%, #bbf7d0 100%) !important;
      box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
    }
  }

  &--select-on-click {
    cursor: pointer;
    user-select: none;

    &:active {
      transform: translateY(1px);
    }
  }

  &:nth-child(even) {
    background-color: #f8fafc;
  }
}

.table-cell {
  padding: 16px 20px;
  vertical-align: top;
  border-right: 1px solid #f1f5f9;
  background: inherit;
  will-change: contents;
  contain: layout style;
  /* Allow text wrapping for better content visibility */
  white-space: normal;
  min-width: 150px;
  overflow: visible;
  /* Improved line height for wrapped text */
  line-height: 1.5;
  /* Better text handling for long content */
  word-wrap: break-word;
  word-break: break-word;

  &:last-child {
    border-right: none;
  }

  &--select {
    width: 60px;
    min-width: 60px;
    max-width: 60px;
    text-align: center;
    padding: 12px;
  }

  &--left {
    text-align: left;
  }

  &--center {
    text-align: center;
  }

  &--right {
    text-align: right;
  }

  &--number {
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
    text-align: right;
  }

  &--date {
    white-space: normal;
    min-width: 120px;
  }

  &--boolean {
    text-align: center;
  }

  &--fixed-left {
    position: sticky;
    left: 0;
    z-index: 10;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
    background: white;
  }

  &--fixed-right {
    position: sticky;
    right: 0;
    z-index: 10;
    box-shadow: -2px 0 4px rgba(0, 0, 0, 0.1);
    background: white;
  }
}

.cell-select {
  display: flex;
  align-items: center;
  justify-content: center;
}

.row-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
  accent-color: #10b981;
  transform: scale(1.1);
  transition: all 0.2s ease;
  border-radius: 3px;

  &:hover {
    transform: scale(1.2);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  }

  &:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.2);
  }

  &:checked {
    background-color: #10b981;
    border-color: #10b981;
  }
}

.cell-content {
  /* Allow text wrapping for better content visibility */
  word-wrap: break-word;
  word-break: break-word;
  white-space: normal;
  line-height: 1.4;
  max-height: none;
  overflow: visible;

  /* Support for RTL languages like Arabic */
  direction: inherit;
  text-align: inherit;

  span {
    display: inline;
    max-width: 100%;
    word-wrap: break-word;
    word-break: break-word;
    white-space: normal;

    /* Support for long text content */
    overflow-wrap: break-word;
    hyphens: auto;
  }
}

/* Search Highlighting */
:deep(.search-highlight) {
  background: #fef3c7;
  color: #92400e;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 600;
}

/* Virtual Column Styles */
.table-cell--virtual {
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    opacity: 0.6;
    border-radius: 0 2px 2px 0;
  }
}

.cell-content--virtual {
  position: relative;
  z-index: 1;
}

.formatted-content--virtual {
  display: inline-block;
  width: 100%;
}

/* Status Badges */
:deep(.status-badge) {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  white-space: nowrap;

  &.status-active {
    background: #d1fae5;
    color: #065f46;
    border: 1px solid #a7f3d0;
  }

  &.status-inactive {
    background: #fee2e2;
    color: #991b1b;
    border: 1px solid #fca5a5;
  }

  &.status-pending {
    background: #fef3c7;
    color: #92400e;
    border: 1px solid #fcd34d;
  }

  &.status-completed {
    background: #dbeafe;
    color: #1e40af;
    border: 1px solid #93c5fd;
  }

  &.status-cancelled {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }
}

:deep(.status-active) {
  background: #d1fae5;
  color: #065f46;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

:deep(.status-inactive) {
  background: #fee2e2;
  color: #991b1b;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

:deep(.status-pending) {
  background: #fef3c7;
  color: #92400e;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

:deep(.status-completed) {
  background: #dbeafe;
  color: #1e40af;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

:deep(.status-cancelled) {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* Dark Theme */
.vue2-datatable--dark {
  .table-row {
    border-color: #2d3748;

    &:nth-child(even) {
      background-color: #2d3748;
    }

    &--selected {
      background: linear-gradient(135deg, #065f46 0%, #047857 100%) !important;
      border-left-color: #10b981;
      box-shadow: 0 2px 8px rgba(16, 185, 129, 0.25);

      .table-cell {
        color: #d1fae5;
        font-weight: 500;
      }

      .row-checkbox {
        accent-color: #10b981;
      }
    }

    &--hovered {
      background-color: #2d3748;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);

      &.table-row--selected {
        background: linear-gradient(135deg, #047857 0%, #059669 100%) !important;
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
      }
    }
  }

  .table-cell {
    border-color: #2d3748;
    color: white;

    &--fixed-left,
    &--fixed-right {
      background: #1a202c;
    }
  }

  :deep(.search-highlight) {
    background: #92400e;
    color: #fef3c7;
  }
}

/* Compact Theme */
.vue2-datatable--compact {
  .table-cell {
    padding: 8px 12px;
    font-size: 13px;
  }

  .row-checkbox {
    width: 14px;
    height: 14px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .table-cell {
    padding: 10px 12px;
    font-size: 14px;
  }

  .cell-content {
    max-width: 150px;
  }

  .row-checkbox {
    width: 14px;
    height: 14px;
  }
}

@media (max-width: 576px) {
  .table-cell {
    padding: 8px 10px;
    font-size: 13px;
  }

  .cell-content {
    max-width: 100px;
  }

  .row-checkbox {
    width: 12px;
    height: 12px;
  }
}

/* Print Styles */
@media print {
  .table-row {
    &--hovered {
      background-color: transparent !important;
    }
  }

  .row-checkbox {
    display: none;
  }

  .cell-select {
    display: none;
  }

  .table-cell--select {
    display: none;
  }
}

/* Virtual Column Content Styles */
:deep(.currency-value) {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
  font-weight: 600;
  color: #059669;

  &.negative {
    color: #dc2626;
  }

  &.large {
    font-size: 16px;
  }
}

:deep(.percentage-value) {
  font-weight: 600;

  &.positive {
    color: #059669;
  }

  &.negative {
    color: #dc2626;
  }

  &.neutral {
    color: #6b7280;
  }
}

:deep(.profile-card) {
  display: flex;
  align-items: center;
  gap: 8px;

  .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
    border: 2px solid #e5e7eb;
  }

  .info {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .name {
      font-weight: 600;
      color: #374151;
      font-size: 14px;
    }

    .status {
      font-size: 12px;
      font-weight: 500;

      &.active {
        color: #059669;
      }

      &.inactive {
        color: #dc2626;
      }
    }
  }
}

:deep(.skill-tags) {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;

  .skill-tag {
    background: #f3f4f6;
    color: #374151;
    padding: 2px 6px;
    border-radius: 6px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid #e5e7eb;

    &.primary {
      background: #dbeafe;
      color: #1e40af;
      border-color: #93c5fd;
    }

    &.secondary {
      background: #f0fdf4;
      color: #166534;
      border-color: #bbf7d0;
    }
  }
}

:deep(.rating-stars) {
  display: inline-flex;
  align-items: center;
  gap: 2px;

  .star {
    color: #fbbf24;
    font-size: 14px;
  }

  .score {
    margin-left: 4px;
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
  }
}

:deep(.priority-indicator) {
  display: inline-flex;
  align-items: center;
  gap: 4px;

  .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;

    &.high {
      background: #dc2626;
    }

    &.medium {
      background: #f59e0b;
    }

    &.low {
      background: #059669;
    }
  }

  .label {
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

:deep(.progress-bar) {
  display: flex;
  align-items: center;
  gap: 8px;

  .bar {
    flex: 1;
    height: 6px;
    background: #f3f4f6;
    border-radius: 3px;
    overflow: hidden;

    .fill {
      height: 100%;
      background: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
      border-radius: 3px;
      transition: width 0.3s ease;
    }
  }

  .percentage {
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    min-width: 35px;
  }
}

/* Performance Optimizations */
.table-row {
  contain: layout style;
}

.table-cell {
  contain: layout style;
}
</style>
