/**
 * Styling Helper Utilities for Vue2DataTable Virtual Columns
 * Provides common formatting and styling functions for virtual columns
 */

/**
 * Currency formatter with styling
 * @param {number} value - The currency value
 * @param {Object} options - Formatting options
 * @returns {string} Formatted HTML string
 */
export function currencyFormatter(value, options = {}) {
  const {
    currency = 'USD',
    locale = 'en-US',
    className = 'currency-value',
    showSign = false,
    decimals = 2
  } = options

  if (value === null || value === undefined || isNaN(value)) {
    return `<span class="${className} invalid">-</span>`
  }

  const formatted = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency,
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(Math.abs(value))

  const sign = value < 0 ? '-' : (showSign && value > 0 ? '+' : '')
  const colorClass = value < 0 ? 'negative' : value > 0 ? 'positive' : 'neutral'
  const sizeClass = Math.abs(value) >= 100000 ? 'large' : ''

  return `<span class="${className} ${colorClass} ${sizeClass}">${sign}${formatted}</span>`
}

/**
 * Status badge formatter
 * @param {string} status - The status value
 * @param {Object} options - Formatting options
 * @returns {string} Formatted HTML string
 */
export function statusBadgeFormatter(status, options = {}) {
  const {
    showIcon = true,
    customConfig = {},
    className = 'status-badge'
  } = options

  const defaultConfig = {
    active: { icon: '🟢', class: 'status-active', label: 'Active' },
    inactive: { icon: '🔴', class: 'status-inactive', label: 'Inactive' },
    pending: { icon: '⏳', class: 'status-pending', label: 'Pending' },
    completed: { icon: '✅', class: 'status-completed', label: 'Completed' },
    cancelled: { icon: '❌', class: 'status-cancelled', label: 'Cancelled' },
    draft: { icon: '📝', class: 'status-draft', label: 'Draft' },
    review: { icon: '👀', class: 'status-review', label: 'In Review' }
  }

  const config = { ...defaultConfig, ...customConfig }
  const statusConfig = config[status?.toLowerCase()] || config.pending
  const icon = showIcon ? `<span class="status-icon">${statusConfig.icon}</span>` : ''
  const label = statusConfig.label || status

  return `
    <span class="${className} ${statusConfig.class}">
      ${icon}
      <span class="status-label">${label}</span>
    </span>
  `
}

/**
 * Progress bar formatter
 * @param {number} percentage - Progress percentage (0-100)
 * @param {Object} options - Formatting options
 * @returns {string} Formatted HTML string
 */
export function progressBarFormatter(percentage, options = {}) {
  const {
    showPercentage = true,
    height = '8px',
    color = null,
    className = 'progress-bar',
    animated = false
  } = options

  // Determine color based on percentage if not provided
  const progressColor = color || (
    percentage >= 80 ? '#10b981' :
    percentage >= 60 ? '#f59e0b' :
    percentage >= 40 ? '#f97316' : '#ef4444'
  )

  const animatedClass = animated ? 'animated' : ''
  const percentageText = showPercentage ? 
    `<span class="percentage">${Math.round(percentage)}%</span>` : ''

  return `
    <div class="${className} ${animatedClass}">
      <div class="bar" style="height: ${height};">
        <div class="fill" style="width: ${percentage}%; background: ${progressColor};"></div>
      </div>
      ${percentageText}
    </div>
  `
}

/**
 * Priority indicator formatter
 * @param {string} priority - Priority level
 * @param {Object} options - Formatting options
 * @returns {string} Formatted HTML string
 */
export function priorityFormatter(priority, options = {}) {
  const { showLabel = true, className = 'priority-indicator' } = options

  const priorityConfig = {
    low: { color: '#10b981', label: 'Low', icon: '🟢' },
    medium: { color: '#f59e0b', label: 'Medium', icon: '🟡' },
    high: { color: '#ef4444', label: 'High', icon: '🔴' },
    urgent: { color: '#dc2626', label: 'URGENT', icon: '🚨' }
  }

  const config = priorityConfig[priority?.toLowerCase()] || priorityConfig.medium
  const label = showLabel ? `<span class="label">${config.label}</span>` : ''

  return `
    <div class="${className} priority-${priority?.toLowerCase()}">
      <span class="dot" style="background-color: ${config.color};"></span>
      ${label}
    </div>
  `
}

/**
 * Rating stars formatter
 * @param {number} rating - Rating value (0-5 or 0-100)
 * @param {Object} options - Formatting options
 * @returns {string} Formatted HTML string
 */
export function ratingFormatter(rating, options = {}) {
  const {
    maxRating = 5,
    showScore = true,
    className = 'rating-stars',
    starIcon = '⭐',
    emptyStarIcon = '☆'
  } = options

  // Convert percentage to 5-star scale if needed
  const normalizedRating = rating > maxRating ? Math.round(rating / 20) : rating
  const fullStars = Math.floor(normalizedRating)
  const hasHalfStar = normalizedRating % 1 >= 0.5
  const emptyStars = maxRating - fullStars - (hasHalfStar ? 1 : 0)

  const stars = [
    starIcon.repeat(fullStars),
    hasHalfStar ? '⭐' : '',
    emptyStarIcon.repeat(emptyStars)
  ].join('')

  const score = showScore ? `<span class="score">${rating}${rating <= maxRating ? '' : '%'}</span>` : ''

  return `
    <div class="${className}">
      <span class="stars">${stars}</span>
      ${score}
    </div>
  `
}

/**
 * Profile card formatter
 * @param {Object} item - Data item
 * @param {Object} options - Formatting options
 * @returns {string} Formatted HTML string
 */
export function profileCardFormatter(item, options = {}) {
  const {
    showAvatar = true,
    showStatus = true,
    avatarField = 'avatar',
    nameFields = ['first_name', 'last_name'],
    statusField = 'status',
    className = 'profile-card'
  } = options

  const name = nameFields.map(field => item[field]).filter(Boolean).join(' ')
  const avatar = item[avatarField] || generateAvatarPlaceholder(name)
  const status = item[statusField]

  const avatarHtml = showAvatar ? `
    <div class="avatar-container">
      ${avatar.startsWith('http') || avatar.startsWith('/') ? 
        `<img src="${avatar}" alt="Avatar" class="avatar" />` :
        `<div class="avatar-placeholder">${avatar}</div>`
      }
    </div>
  ` : ''

  const statusHtml = showStatus && status ? `
    <div class="status ${status}">${status}</div>
  ` : ''

  return `
    <div class="${className}">
      ${avatarHtml}
      <div class="info">
        <div class="name">${name}</div>
        ${statusHtml}
      </div>
    </div>
  `
}

/**
 * Generate avatar placeholder from name
 * @param {string} name - Full name
 * @returns {string} Initials for avatar placeholder
 */
function generateAvatarPlaceholder(name) {
  return name
    .split(' ')
    .map(word => word[0])
    .join('')
    .toUpperCase()
    .slice(0, 2)
}

/**
 * Skill tags formatter
 * @param {Array} skills - Array of skills
 * @param {Object} options - Formatting options
 * @returns {string} Formatted HTML string
 */
export function skillTagsFormatter(skills, options = {}) {
  const {
    maxVisible = 3,
    className = 'skill-tags',
    tagClassName = 'skill-tag'
  } = options

  if (!Array.isArray(skills) || skills.length === 0) {
    return `<div class="${className}">No skills listed</div>`
  }

  const visibleSkills = skills.slice(0, maxVisible)
  const remainingCount = skills.length - maxVisible

  const skillTags = visibleSkills.map((skill, index) => {
    const tagClass = index === 0 ? `${tagClassName} primary` : 
                    index === 1 ? `${tagClassName} secondary` : tagClassName
    return `<span class="${tagClass}">${skill}</span>`
  }).join('')

  const moreTag = remainingCount > 0 ? 
    `<span class="${tagClassName} more">+${remainingCount} more</span>` : ''

  return `<div class="${className}">${skillTags}${moreTag}</div>`
}

/**
 * Generate conditional CSS classes based on value ranges
 * @param {number} value - The value to evaluate
 * @param {Array} ranges - Array of range objects with min, max, and class properties
 * @param {string} baseClass - Base CSS class to always include
 * @returns {Array} Array of CSS classes
 */
export function generateRangeClasses(value, ranges, baseClass = '') {
  const classes = baseClass ? [baseClass] : []

  for (const range of ranges) {
    if (value >= range.min && (range.max === undefined || value <= range.max)) {
      classes.push(range.class)
      break
    }
  }

  return classes
}

/**
 * Generate conditional CSS classes based on multiple conditions
 * @param {Object} item - Data item
 * @param {Array} conditions - Array of condition objects
 * @param {string} baseClass - Base CSS class
 * @returns {Array} Array of CSS classes
 */
export function generateConditionalClasses(item, conditions, baseClass = '') {
  const classes = baseClass ? [baseClass] : []

  conditions.forEach(condition => {
    if (condition.test(item)) {
      if (Array.isArray(condition.class)) {
        classes.push(...condition.class)
      } else {
        classes.push(condition.class)
      }
    }
  })

  return classes
}

/**
 * Format date with relative time
 * @param {Date|string} date - Date to format
 * @param {Object} options - Formatting options
 * @returns {string} Formatted HTML string
 */
export function relativeDateFormatter(date, options = {}) {
  const {
    className = 'relative-date',
    showTooltip = true
  } = options

  const dateObj = new Date(date)
  const now = new Date()
  const diffMs = now - dateObj
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  let relativeText
  if (diffDays === 0) {
    relativeText = 'Today'
  } else if (diffDays === 1) {
    relativeText = 'Yesterday'
  } else if (diffDays < 7) {
    relativeText = `${diffDays} days ago`
  } else if (diffDays < 30) {
    const weeks = Math.floor(diffDays / 7)
    relativeText = `${weeks} week${weeks > 1 ? 's' : ''} ago`
  } else {
    relativeText = dateObj.toLocaleDateString()
  }

  const tooltip = showTooltip ? `title="${dateObj.toLocaleString()}"` : ''

  return `<span class="${className}" ${tooltip}>${relativeText}</span>`
}

// Export all formatters as a collection
export const formatters = {
  currency: currencyFormatter,
  statusBadge: statusBadgeFormatter,
  progressBar: progressBarFormatter,
  priority: priorityFormatter,
  rating: ratingFormatter,
  profileCard: profileCardFormatter,
  skillTags: skillTagsFormatter,
  relativeDate: relativeDateFormatter
}

// Export utility functions
export const utilities = {
  generateRangeClasses,
  generateConditionalClasses,
  generateAvatarPlaceholder
}
