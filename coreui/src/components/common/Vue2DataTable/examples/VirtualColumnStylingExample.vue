<template>
  <div class="virtual-column-styling-example">
    <h2>Vue2DataTable Virtual Column Styling Examples</h2>
    
    <p class="description">
      This example demonstrates various styling techniques for virtual columns including 
      custom CSS classes, HTML content, conditional styling, and responsive design.
    </p>

    <!-- Example 1: Basic CSS Class Application -->
    <div class="example-section">
      <h3>Example 1: Custom CSS Classes</h3>
      <Vue2DataTable
        :columns="basicStyledColumns"
        :data-source="sampleData"
        :height="400"
        :searchable="true"
        :sortable="true"
        :show-total-bar="true"
      />
    </div>

    <!-- Example 2: HTML Content with Rich Styling -->
    <div class="example-section">
      <h3>Example 2: HTML Content & Rich Styling</h3>
      <Vue2DataTable
        :columns="richContentColumns"
        :data-source="sampleData"
        :height="400"
        :searchable="true"
        :sortable="true"
        :show-total-bar="true"
      />
    </div>

    <!-- Example 3: Conditional Styling -->
    <div class="example-section">
      <h3>Example 3: Conditional Styling</h3>
      <Vue2DataTable
        :columns="conditionalStyledColumns"
        :data-source="sampleData"
        :height="400"
        :searchable="true"
        :sortable="true"
        :show-total-bar="true"
      />
    </div>

    <!-- Styling Guide -->
    <div class="styling-guide">
      <h3>Styling Methods</h3>
      
      <div class="method-card">
        <h4>1. Cell-Level CSS Classes</h4>
        <pre><code>{{cellClassExample}}</code></pre>
      </div>

      <div class="method-card">
        <h4>2. Content-Level CSS Classes</h4>
        <pre><code>{{contentClassExample}}</code></pre>
      </div>

      <div class="method-card">
        <h4>3. HTML Content with Inline Styles</h4>
        <pre><code>{{htmlContentExample}}</code></pre>
      </div>

      <div class="method-card">
        <h4>4. Conditional Styling Functions</h4>
        <pre><code>{{conditionalStyleExample}}</code></pre>
      </div>
    </div>
  </div>
</template>

<script>
import Vue2DataTable from '../components/core/Vue2DataTable.vue'

export default {
  name: 'VirtualColumnStylingExample',
  
  components: {
    Vue2DataTable
  },

  data() {
    return {
      // Sample data
      sampleData: [
        {
          id: 1,
          first_name: 'John',
          last_name: 'Doe',
          email: '<EMAIL>',
          salary: 75000,
          performance: 85,
          department: 'Engineering',
          status: 'active',
          priority: 'high',
          skills: ['JavaScript', 'Vue.js', 'Node.js'],
          avatar: '/avatars/john.jpg',
          join_date: '2020-01-15'
        },
        {
          id: 2,
          first_name: 'Jane',
          last_name: 'Smith',
          email: '<EMAIL>',
          salary: 82000,
          performance: 92,
          department: 'Design',
          status: 'active',
          priority: 'medium',
          skills: ['UI/UX', 'Figma', 'Adobe Creative Suite'],
          avatar: '/avatars/jane.jpg',
          join_date: '2019-03-22'
        },
        {
          id: 3,
          first_name: 'Bob',
          last_name: 'Johnson',
          email: '<EMAIL>',
          salary: 95000,
          performance: 78,
          department: 'Engineering',
          status: 'inactive',
          priority: 'low',
          skills: ['Python', 'Django', 'PostgreSQL'],
          avatar: '/avatars/bob.jpg',
          join_date: '2018-07-10'
        }
      ],

      // Basic styled columns with CSS classes
      basicStyledColumns: [
        {
          key: 'id',
          label: 'ID',
          width: '80px'
        },
        {
          key: 'full_name',
          label: 'Full Name',
          virtual: true,
          formatter: (item) => `${item.first_name} ${item.last_name}`,
          cellClass: 'custom-name-cell',
          contentClass: 'name-content',
          width: '200px'
        },
        {
          key: 'salary_display',
          label: 'Salary',
          virtual: true,
          formatter: (item) => {
            return new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD'
            }).format(item.salary)
          },
          cellClass: 'salary-cell',
          formattedClass: 'currency-value',
          width: '150px'
        },
        {
          key: 'status_badge',
          label: 'Status',
          virtual: true,
          formatter: (item) => {
            const statusClass = `status-badge status-${item.status}`
            const icon = item.status === 'active' ? '🟢' : '🔴'
            return `<span class="${statusClass}">${icon} ${item.status.toUpperCase()}</span>`
          },
          width: '120px'
        }
      ],

      // Rich content columns with HTML
      richContentColumns: [
        {
          key: 'profile_card',
          label: 'Employee Profile',
          virtual: true,
          formatter: (item) => {
            return `
              <div class="profile-card">
                <div class="avatar-placeholder">${item.first_name[0]}${item.last_name[0]}</div>
                <div class="info">
                  <div class="name">${item.first_name} ${item.last_name}</div>
                  <div class="status ${item.status}">${item.status}</div>
                </div>
              </div>
            `
          },
          width: '250px'
        },
        {
          key: 'skills_display',
          label: 'Skills',
          virtual: true,
          formatter: (item) => {
            const skillTags = item.skills.slice(0, 2).map((skill, index) => {
              const tagClass = index === 0 ? 'skill-tag primary' : 'skill-tag secondary'
              return `<span class="${tagClass}">${skill}</span>`
            }).join('')
            
            const moreCount = item.skills.length - 2
            const moreTag = moreCount > 0 ? `<span class="skill-tag">+${moreCount} more</span>` : ''
            
            return `<div class="skill-tags">${skillTags}${moreTag}</div>`
          },
          width: '200px'
        },
        {
          key: 'performance_bar',
          label: 'Performance',
          virtual: true,
          formatter: (item) => {
            const percentage = item.performance
            return `
              <div class="progress-bar">
                <div class="bar">
                  <div class="fill" style="width: ${percentage}%"></div>
                </div>
                <span class="percentage">${percentage}%</span>
              </div>
            `
          },
          width: '180px'
        }
      ],

      // Conditional styled columns
      conditionalStyledColumns: [
        {
          key: 'employee_name',
          label: 'Employee',
          virtual: true,
          formatter: (item) => `${item.first_name} ${item.last_name}`,
          cellClass: (item) => {
            return item.status === 'active' ? 'active-employee' : 'inactive-employee'
          },
          width: '200px'
        },
        {
          key: 'salary_conditional',
          label: 'Salary',
          virtual: true,
          formatter: (item) => {
            const formatted = new Intl.NumberFormat('en-US', {
              style: 'currency',
              currency: 'USD'
            }).format(item.salary)
            
            return formatted
          },
          cellClass: (item) => {
            if (item.salary >= 90000) return 'high-salary'
            if (item.salary >= 80000) return 'medium-salary'
            return 'low-salary'
          },
          formattedClass: (item) => {
            const baseClass = 'currency-value'
            if (item.salary >= 90000) return [baseClass, 'large']
            return baseClass
          },
          width: '150px'
        },
        {
          key: 'priority_indicator',
          label: 'Priority',
          virtual: true,
          formatter: (item) => {
            return `
              <div class="priority-indicator">
                <span class="dot ${item.priority}"></span>
                <span class="label">${item.priority}</span>
              </div>
            `
          },
          cellClass: (item) => `priority-${item.priority}`,
          width: '120px'
        },
        {
          key: 'performance_rating',
          label: 'Rating',
          virtual: true,
          formatter: (item) => {
            const stars = Math.ceil(item.performance / 20)
            const starIcons = '⭐'.repeat(stars)
            return `
              <div class="rating-stars">
                <span class="stars">${starIcons}</span>
                <span class="score">${item.performance}%</span>
              </div>
            `
          },
          cellClass: (item) => {
            if (item.performance >= 90) return 'excellent-performance'
            if (item.performance >= 80) return 'good-performance'
            if (item.performance >= 70) return 'average-performance'
            return 'poor-performance'
          },
          width: '150px'
        }
      ]
    }
  },

  computed: {
    cellClassExample() {
      return `{
  key: 'full_name',
  label: 'Full Name',
  virtual: true,
  formatter: (item) => \`\${item.first_name} \${item.last_name}\`,
  cellClass: 'custom-name-cell', // Static class
  // OR
  cellClass: (item) => item.active ? 'active-cell' : 'inactive-cell'
}`
    },

    contentClassExample() {
      return `{
  key: 'salary',
  label: 'Salary',
  virtual: true,
  formatter: (item) => formatCurrency(item.salary),
  contentClass: 'salary-content',
  formattedClass: 'currency-value'
}`
    },

    htmlContentExample() {
      return `{
  key: 'profile',
  label: 'Profile',
  virtual: true,
  formatter: (item) => \`
    <div class="profile-card">
      <img src="\${item.avatar}" class="avatar" />
      <div class="info">
        <strong>\${item.name}</strong>
        <span class="status \${item.status}">\${item.status}</span>
      </div>
    </div>
  \`
}`
    },

    conditionalStyleExample() {
      return `{
  key: 'performance',
  label: 'Performance',
  virtual: true,
  formatter: (item) => \`\${item.score}%\`,
  cellClass: (item) => {
    if (item.score >= 90) return 'excellent'
    if (item.score >= 70) return 'good'
    return 'needs-improvement'
  },
  formattedClass: (item) => [
    'score-value',
    item.score >= 80 ? 'positive' : 'negative'
  ]
}`
    }
  }
}
</script>

<style lang="scss" scoped>
.virtual-column-styling-example {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.description {
  background: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
  margin-bottom: 32px;
  color: #374151;
  font-size: 14px;
  line-height: 1.6;
}

.example-section {
  margin-bottom: 48px;
  
  h3 {
    color: #1f2937;
    margin-bottom: 16px;
    font-size: 18px;
    font-weight: 600;
  }
}

.styling-guide {
  margin-top: 48px;
  
  h3 {
    color: #1f2937;
    margin-bottom: 24px;
    font-size: 20px;
    font-weight: 600;
  }
}

.method-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  
  h4 {
    color: #374151;
    margin-bottom: 12px;
    font-size: 16px;
    font-weight: 600;
  }
  
  pre {
    background: #1f2937;
    color: #f9fafb;
    padding: 16px;
    border-radius: 6px;
    overflow-x: auto;
    font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Fira Code', monospace;
    font-size: 13px;
    line-height: 1.5;
    margin: 0;
    
    code {
      background: none;
      color: inherit;
      padding: 0;
    }
  }
}

h2 {
  color: #1f2937;
  margin-bottom: 24px;
  font-size: 24px;
  font-weight: 700;
}

/* Custom styles for virtual columns */
:deep(.custom-name-cell) {
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-left: 3px solid #0ea5e9;
}

:deep(.name-content) {
  font-weight: 600;
  color: #0c4a6e;
}

:deep(.salary-cell) {
  background: #f0fdf4;
  text-align: right;
}

:deep(.active-employee) {
  background: #f0fdf4;
  border-left: 3px solid #22c55e;
}

:deep(.inactive-employee) {
  background: #fef2f2;
  border-left: 3px solid #ef4444;
  opacity: 0.7;
}

:deep(.high-salary) {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  font-weight: 700;
}

:deep(.medium-salary) {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  font-weight: 600;
}

:deep(.low-salary) {
  background: #f9fafb;
  font-weight: 500;
}

:deep(.priority-high) {
  border-left: 4px solid #dc2626;
}

:deep(.priority-medium) {
  border-left: 4px solid #f59e0b;
}

:deep(.priority-low) {
  border-left: 4px solid #059669;
}

:deep(.excellent-performance) {
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

:deep(.good-performance) {
  background: linear-gradient(135deg, #f0f9ff 0%, #dbeafe 100%);
}

:deep(.average-performance) {
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

:deep(.poor-performance) {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

:deep(.avatar-placeholder) {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 12px;
}
</style>
